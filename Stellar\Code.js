
// Константи для ідентифікації таблиці та аркуша
const SPREADSHEET_ID = '1nhLTKEsWa6AAT0xPHqOAc7TQFzkHtqcEu7w7KlAAogU';
const SHEET_NAME = 'Аркуш1';

// Інші константи
const COLUMN_A = 1;
const COLUMN_B = 2;
const COLUMN_C = 3;
const HEADER_ROW = ['Номер машини', 'Номер причепа'];
const DIGIT_PATTERN = /\d{4}/g;
const LETTER_PATTERN = /[a-zA-Zа-яА-ЯіІїЇєЄ]/;

function processVehicleNumbers() {
  // Отримуємо потрібний аркуш через константи
  const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);

  // Отримуємо всі дані зі стовбця A
  const lastRow = sheet.getLastRow();
  const textData = sheet.getRange(1, COLUMN_A, lastRow, 1).getValues();

  // Масив для зберігання результатів
  const results = [];
  // Додаємо заголовки для стовпців B і C
  results.push(HEADER_ROW);
  // Проходимо по всіх рядках
  for (let i = 0; i < textData.length; i++) {
    const text = textData[i][0].toString();

    if (text.trim() === '') {
      results.push(['', '']); // Порожні стовбці для порожніх рядків
      continue;
    }

    // Знаходимо всі валідні номери (букви + 4 цифри + букви)
    const validNumbers = findValidVehicleNumbers(text);

    let vehicleNumber = '';
    let trailerNumber = '';

    // Записуємо дані тільки якщо знайдено мінімум 2 номери (авто + причеп)
    if (validNumbers.length >= 2) {
      vehicleNumber = validNumbers[0];  // Перший номер - машина
      trailerNumber = validNumbers[1];  // Другий номер - причеп
    }

    results.push([vehicleNumber, trailerNumber]);
  }

  // Записуємо результати в стовбці B і C
  if (results.length > 0) {
    sheet.getRange(1, COLUMN_B, results.length, 2).setValues(results);
  }

  Logger.log('Обробка завершена. Оброблено рядків: ' + results.length);
}

function findValidVehicleNumbers(text) {
  const validNumbers = [];

  // Знаходимо всі послідовності з 4 цифр
  let match;

  while ((match = DIGIT_PATTERN.exec(text)) !== null) {
    const digits = match[0];
    const startPos = match.index;
    const endPos = startPos + 4;

    // Шукаємо букви перед цифрами (мінімум 1, максимум 2)
    let lettersBefore = '';
    for (let i = startPos - 1; i >= 0; i--) {
      const char = text[i];
      if (LETTER_PATTERN.test(char)) {
        lettersBefore = char + lettersBefore;
        if (lettersBefore.length === 2) break;
      } else if (char !== ' ') {
        // Якщо зустрічаємо не букву і не пробіл - зупиняємося
        break;
      }
    }

    // Шукаємо букви після цифр (мінімум 1, максимум 2)
    let lettersAfter = '';
    for (let i = endPos; i < text.length; i++) {
      const char = text[i];
      if (LETTER_PATTERN.test(char)) {
        lettersAfter += char;
        if (lettersAfter.length === 2) break;
      } else if (char !== ' ') {
        // Якщо зустрічаємо не букву і не пробіл - зупиняємося
        break;
      }
    }

    // Перевіряємо чи є хоча б одна буква до І після цифр
    if (lettersBefore.length > 0 && lettersAfter.length > 0) {
      const fullNumber = lettersBefore + digits + lettersAfter;
      validNumbers.push(fullNumber);
    }
  }

  return validNumbers;
}

// дописуємо функцію для запуску в випадаючому меню
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Пошук номерів')
    .addItem('Обробити номери транспортних засобів', 'processVehicleNumbers')
    .addToUi();
}