
// Константи для ідентифікації таблиці та аркуша
const SPREADSHEET_ID = '1nhLTKEsWa6AAT0xPHqOAc7TQFzkHtqcEu7w7KlAAogU';
const SHEET_NAME = 'Аркуш1';
const SETTLEMENTS_SHEET_NAME = 'Населені пункти';

// Інші константи
const COLUMN_A = 1;
const COLUMN_B = 2;
const COLUMN_C = 3;
const COLUMN_D = 4;
const HEADER_ROW = ['Номер машини', 'Номер причепа', 'Населений пункт'];
const DIGIT_PATTERN = /\d{4}/g;
const LETTER_PATTERN = /[a-zA-Zа-яА-ЯіІїЇєЄ]/;

function processVehicleNumbers() {
  // Отримуємо потрібний аркуш через константи
  const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);

  // Отримуємо всі дані зі стовбця A
  const lastRow = sheet.getLastRow();
  const textData = sheet.getRange(1, COLUMN_A, lastRow, 1).getValues();

  // Завантажуємо список населених пунктів
  const settlements = loadSettlements();

  // Масив для зберігання результатів
  const results = [];
  // Додаємо заголовки для стовпців B, C і D
  results.push(HEADER_ROW);
  // Проходимо по всіх рядках
  for (let i = 0; i < textData.length; i++) {
    const text = textData[i][0].toString();

    if (text.trim() === '') {
      results.push(['', '', '']); // Порожні стовбці для порожніх рядків
      continue;
    }

    // Знаходимо всі валідні номери (букви + 4 цифри + букви)
    const validNumbers = findValidVehicleNumbers(text);

    let vehicleNumber = '';
    let trailerNumber = '';

    // Записуємо дані тільки якщо знайдено мінімум 2 номери (авто + причеп)
    if (validNumbers.length >= 2) {
      vehicleNumber = validNumbers[0];  // Перший номер - машина
      trailerNumber = validNumbers[1];  // Другий номер - причеп
    }

    // Шукаємо населений пункт у тексті
    const foundSettlement = findSettlementInText(text, settlements);

    results.push([vehicleNumber, trailerNumber, foundSettlement]);
  }

  // Записуємо результати в стовбці B, C і D
  if (results.length > 0) {
    sheet.getRange(1, COLUMN_B, results.length, 3).setValues(results);
  }

  Logger.log('Обробка завершена. Оброблено рядків: ' + results.length);
}

function findValidVehicleNumbers(text) {
  const validNumbers = [];

  // Знаходимо всі послідовності з 4 цифр
  let match;

  while ((match = DIGIT_PATTERN.exec(text)) !== null) {
    const digits = match[0];
    const startPos = match.index;
    const endPos = startPos + 4;

    // Шукаємо букви перед цифрами (мінімум 1, максимум 2)
    let lettersBefore = '';
    for (let i = startPos - 1; i >= 0; i--) {
      const char = text[i];
      if (LETTER_PATTERN.test(char)) {
        lettersBefore = char + lettersBefore;
        if (lettersBefore.length === 2) break;
      } else if (char !== ' ') {
        // Якщо зустрічаємо не букву і не пробіл - зупиняємося
        break;
      }
    }

    // Шукаємо букви після цифр (мінімум 1, максимум 2)
    let lettersAfter = '';
    for (let i = endPos; i < text.length; i++) {
      const char = text[i];
      if (LETTER_PATTERN.test(char)) {
        lettersAfter += char;
        if (lettersAfter.length === 2) break;
      } else if (char !== ' ') {
        // Якщо зустрічаємо не букву і не пробіл - зупиняємося
        break;
      }
    }

    // Перевіряємо чи є хоча б одна буква до І після цифр
    if (lettersBefore.length > 0 && lettersAfter.length > 0) {
      const fullNumber = lettersBefore + digits + lettersAfter;
      validNumbers.push(fullNumber);
    }
  }

  return validNumbers;
}

// Функція для завантаження списку населених пунктів
function loadSettlements() {
  try {
    const settlementsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SETTLEMENTS_SHEET_NAME);

    if (!settlementsSheet) {
      Logger.log('Аркуш "Населені пункти" не знайдено');
      return [];
    }

    const lastRow = settlementsSheet.getLastRow();
    if (lastRow < 1) {
      Logger.log('Аркуш "Населені пункти" порожній');
      return [];
    }

    // Отримуємо всі дані зі стовпця A (населені пункти)
    const settlementsData = settlementsSheet.getRange(1, 1, lastRow, 1).getValues();

    // Перетворюємо в масив рядків та очищуємо від порожніх значень
    const settlements = settlementsData
      .map(row => row[0].toString().trim())
      .filter(settlement => settlement !== '');

    Logger.log('Завантажено населених пунктів: ' + settlements.length);
    return settlements;
  } catch (error) {
    Logger.log('Помилка при завантаженні населених пунктів: ' + error.toString());
    return [];
  }
}

// Функція для пошуку населених пунктів у тексті
function findSettlementInText(text, settlements) {
  if (!text || !settlements || settlements.length === 0) {
    return '';
  }

  // Нормалізуємо текст для пошуку
  const normalizedText = text.toLowerCase().trim();

  // Шукаємо найдовший збіг серед населених пунктів
  let foundSettlement = '';
  let maxLength = 0;

  for (const settlement of settlements) {
    const normalizedSettlement = settlement.toLowerCase().trim();

    // Пропускаємо порожні записи
    if (normalizedSettlement === '') continue;

    // Перевіряємо чи містить текст назву населеного пункту
    if (normalizedText.includes(normalizedSettlement)) {
      // Вибираємо найдовший збіг (найбільш специфічний)
      if (normalizedSettlement.length > maxLength) {
        foundSettlement = settlement; // Повертаємо оригінальний регістр
        maxLength = normalizedSettlement.length;
      }
    }
  }

  return foundSettlement;
}

// Тестова функція для перевірки пошуку населених пунктів
function testSettlementSearch() {
  // Тестові дані
  const testSettlements = ['с. Азовське', 'Бердянський', 'Запорізька область', 'м. Київ', 'Вільногірськ', 'Степанки'];
  const testTexts = [
    'Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ',
    'Транспортна послуга у міжнародному сполученні с. Азовське - м. Київ',
    'Доставка товару в Бердянський район'
  ];

  Logger.log('=== Тестування пошуку населених пунктів ===');

  for (let i = 0; i < testTexts.length; i++) {
    const text = testTexts[i];
    const found = findSettlementInText(text, testSettlements);
    Logger.log(`Текст: ${text}`);
    Logger.log(`Знайдено: ${found}`);
    Logger.log('---');
  }
}

// дописуємо функцію для запуску в випадаючому меню
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Пошук номерів')
    .addItem('Обробити номери транспортних засобів та населені пункти', 'processVehicleNumbers')
    .addSeparator()
    .addItem('Тестувати пошук населених пунктів', 'testSettlementSearch')
    .addToUi();
}