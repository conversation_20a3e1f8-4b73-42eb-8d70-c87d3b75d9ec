
// Константи для ідентифікації таблиці та аркуша
const SPREADSHEET_ID = '1nhLTKEsWa6AAT0xPHqOAc7TQFzkHtqcEu7w7KlAAogU';
const SHEET_NAME = 'Аркуш1';
const SETTLEMENTS_SHEET_NAME = 'Населені пункти';

// Інші константи
const COLUMN_A = 1;
const COLUMN_B = 2;
const COLUMN_C = 3;
const COLUMN_D = 4;
const HEADER_ROW = ['Номер машини', 'Номер причепа', 'Населений пункт1', 'Населений пункт2'];
const DIGIT_PATTERN = /\d{4}/g;
const LETTER_PATTERN = /[a-zA-Zа-яА-ЯіІїЇєЄ]/;

function processVehicleNumbers() {
  // Отримуємо потрібний аркуш через константи
  const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SHEET_NAME);

  // Отримуємо всі дані зі стовбця A
  const lastRow = sheet.getLastRow();
  const textData = sheet.getRange(1, COLUMN_A, lastRow, 1).getValues();

  // Завантажуємо список населених пунктів
  const settlements = loadSettlements();

  // Масив для зберігання результатів
  const results = [];
  // Додаємо заголовки для стовпців B, C і D
  results.push(HEADER_ROW);
  // Проходимо по всіх рядках
  for (let i = 0; i < textData.length; i++) {
    const text = textData[i][0].toString();

    if (text.trim() === '') {
      results.push(['', '', '']); // Порожні стовбці для порожніх рядків
      continue;
    }

    // Знаходимо всі валідні номери (букви + 4 цифри + букви)
    const validNumbers = findValidVehicleNumbers(text);

    let vehicleNumber = '';
    let trailerNumber = '';

    // Записуємо дані тільки якщо знайдено мінімум 2 номери (авто + причеп)
    if (validNumbers.length >= 2) {
      vehicleNumber = validNumbers[0];  // Перший номер - машина
      trailerNumber = validNumbers[1];  // Другий номер - причеп
    }

    // Шукаємо населені пункти у тексті
    const foundSettlements = findSettlementInText(text, settlements);

    results.push([vehicleNumber, trailerNumber, foundSettlements[0] || '', foundSettlements[1] || '']);
  }

  // Записуємо результати в стовбці B, C, D і E
  if (results.length > 0) {
    sheet.getRange(1, COLUMN_B, results.length, 4).setValues(results);
  }

  Logger.log('Обробка завершена. Оброблено рядків: ' + results.length);
}

function findValidVehicleNumbers(text) {
  const validNumbers = [];

  // Знаходимо всі послідовності з 4 цифр
  let match;

  while ((match = DIGIT_PATTERN.exec(text)) !== null) {
    const digits = match[0];
    const startPos = match.index;
    const endPos = startPos + 4;

    // Шукаємо букви перед цифрами (мінімум 1, максимум 2)
    let lettersBefore = '';
    for (let i = startPos - 1; i >= 0; i--) {
      const char = text[i];
      if (LETTER_PATTERN.test(char)) {
        lettersBefore = char + lettersBefore;
        if (lettersBefore.length === 2) break;
      } else if (char !== ' ') {
        // Якщо зустрічаємо не букву і не пробіл - зупиняємося
        break;
      }
    }

    // Шукаємо букви після цифр (мінімум 1, максимум 2)
    let lettersAfter = '';
    for (let i = endPos; i < text.length; i++) {
      const char = text[i];
      if (LETTER_PATTERN.test(char)) {
        lettersAfter += char;
        if (lettersAfter.length === 2) break;
      } else if (char !== ' ') {
        // Якщо зустрічаємо не букву і не пробіл - зупиняємося
        break;
      }
    }

    // Перевіряємо чи є хоча б одна буква до І після цифр
    if (lettersBefore.length > 0 && lettersAfter.length > 0) {
      const fullNumber = lettersBefore + digits + lettersAfter;
      validNumbers.push(fullNumber);
    }
  }

  return validNumbers;
}

// Функція для завантаження списку населених пунктів
function loadSettlements() {
  try {
    const settlementsSheet = SpreadsheetApp.openById(SPREADSHEET_ID).getSheetByName(SETTLEMENTS_SHEET_NAME);

    if (!settlementsSheet) {
      Logger.log('Аркуш "Населені пункти" не знайдено');
      return [];
    }

    const lastRow = settlementsSheet.getLastRow();
    if (lastRow < 1) {
      Logger.log('Аркуш "Населені пункти" порожній');
      return [];
    }

    // Отримуємо всі дані зі стовпця A (населені пункти)
    const settlementsData = settlementsSheet.getRange(1, 1, lastRow, 1).getValues();

    // Перетворюємо в масив рядків та очищуємо від порожніх значень
    const settlements = settlementsData
      .map(row => row[0].toString().trim())
      .filter(settlement => settlement !== '');

    Logger.log('Завантажено населених пунктів: ' + settlements.length);
    return settlements;
  } catch (error) {
    Logger.log('Помилка при завантаженні населених пунктів: ' + error.toString());
    return [];
  }
}

// Функція для нормалізації тексту населених пунктів
function normalizeSettlementText(text) {
  return text
    .toLowerCase()
    .trim()
    // Видаляємо зайві пробіли
    .replace(/\s+/g, ' ')
    // Нормалізуємо префікси - додаємо пробіл після крапки якщо його немає
    .replace(/([смт]+\.?)([а-яіїєґ])/g, '$1 $2')
    // Видаляємо пробіли перед комами та крапками
    .replace(/\s+([,.;])/g, '$1')
    // Видаляємо зайві розділові знаки
    .replace(/[,.;]+$/, '');
}

// Функція для створення варіантів написання населеного пункту
function generateSettlementVariants(settlement) {
  const variants = [];
  const normalized = normalizeSettlementText(settlement);

  // Додаємо оригінальний варіант
  variants.push(normalized);

  // Варіанти з різними пробілами після префіксів
  const withoutSpaces = normalized.replace(/([смт]+\.)\s+/g, '$1');
  const withSpaces = normalized.replace(/([смт]+\.)([а-яіїєґ])/g, '$1 $2');

  variants.push(withoutSpaces);
  variants.push(withSpaces);

  // Варіант без префіксу взагалі (тільки назва)
  const withoutPrefix = normalized.replace(/^[смт]+\.?\s*/, '');
  if (withoutPrefix.length > 2) { // Тільки якщо назва достатньо довга
    variants.push(withoutPrefix);
  }

  // Видаляємо дублікати
  return [...new Set(variants)].filter(v => v.length > 0);
}

// Функція для пошуку двох населених пунктів у тексті по словах
function findSettlementInText(text, settlements) {
  if (!text || !settlements || settlements.length === 0) {
    return ['', ''];
  }

  // Розбиваємо текст на слова (довжиною більше 4 символів)
  const words = text
    .toLowerCase()
    .replace(/[^\u0400-\u04FF\u0500-\u052F\s]/g, ' ') // Залишаємо тільки кирилицю та пробіли
    .split(/\s+/)
    .filter(word => word.length > 4); // Тільки слова довше 4 символів

  const foundSettlements = [];

  // Проходимо по кожному слову в тексті
  for (const word of words) {
    // Шукаємо це слово серед населених пунктів
    for (const settlement of settlements) {
      if (!settlement || settlement.trim() === '') continue;

      // Нормалізуємо назву населеного пункту для порівняння
      const normalizedSettlement = settlement.toLowerCase().trim();

      // Перевіряємо точний збіг або часткове включення
      if (word === normalizedSettlement ||
          word.includes(normalizedSettlement) ||
          normalizedSettlement.includes(word)) {

        // Додаємо тільки якщо ще не знайшли цей населений пункт
        if (!foundSettlements.includes(settlement)) {
          foundSettlements.push(settlement);

          // Зупиняємося після знаходження 2 населених пунктів
          if (foundSettlements.length >= 2) {
            break;
          }
        }
      }
    }

    // Зупиняємося після знаходження 2 населених пунктів
    if (foundSettlements.length >= 2) {
      break;
    }
  }

  // Повертаємо масив з двох елементів (перший - Населений пункт1, другий - Населений пункт2)
  return [
    foundSettlements[0] || '',
    foundSettlements[1] || ''
  ];
}

// дописуємо функцію для запуску в випадаючому меню
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Пошук номерів')
    .addItem('Обробити номери транспортних засобів та населені пункти', 'processVehicleNumbers')
    .addToUi();
}