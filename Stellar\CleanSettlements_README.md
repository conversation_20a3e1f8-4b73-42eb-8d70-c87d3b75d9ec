# Очищення реєстру населених пунктів

Цей код призначений для очищення реєстру населених пунктів від префіксів типу "с.", "м.", "смт." тощо.

## Що робить код

Код видаляє з назв населених пунктів такі префікси:
- `с.` (село)
- `м.` (місто) 
- `смт.` (селище міського типу)
- `сел.` (село)
- `село ` (повне слово)
- `місто ` (повне слово)
- `селище ` (повне слово)
- `селище міського типу ` (повна фраза)

## Приклади перетворень

| Було | Стало |
|------|-------|
| с. Азовське | Азовське |
| м. Київ | Київ |
| смт. Васильків | Васильків |
| село Петрівка | Петрівка |
| місто Харків | Харків |
| с.Зоря | Зоря |
| м.Одеса | Одеса |
| Вільногірськ | Вільногірськ |
| с. Степанки, | Степанки |

## Як використовувати

### Крок 1: Додати код в Google Apps Script

1. Відкрийте вашу Google Таблицю
2. Перейдіть в **Розширення → Apps Script**
3. Створіть новий файл (File → New → Script file)
4. Назвіть його `CleanSettlements`
5. Скопіюйте код з файлу `CleanSettlements.js`
6. Збережіть проект

### Крок 2: Оновити таблицю

1. Поверніться до Google Таблиці
2. Оновіть сторінку (F5)
3. Ви побачите нове меню **"Очищення реєстру"**

### Крок 3: Виконати очищення

**ВАЖЛИВО: Обов'язково створіть резервну копію!**

1. **Створити резервну копію**: Меню → "1. Створити резервну копію"
2. **Тестувати** (опціонально): Меню → "2. Тестувати очищення (без змін)"
3. **Очистити реєстр**: Меню → "3. Очистити реєстр населених пунктів"

## Безпека

- ✅ Код автоматично створює резервну копію аркуша
- ✅ Є функція тестування без змін
- ✅ Показує детальний лог змін
- ✅ Підраховує кількість змінених записів

## Що відбувається

1. Код читає всі записи з аркуша "Населені пункти"
2. Для кожного запису видаляє префікси
3. Записує очищені дані назад в той самий аркуш
4. Показує звіт про кількість змін

## Логи

Всі зміни записуються в лог. Щоб переглянути:
1. В Google Apps Script натисніть **Ctrl+Enter**
2. Або перейдіть в **View → Logs**

## Відновлення

Якщо щось пішло не так:
1. Видаліть поточний аркуш "Населені пункти"
2. Перейменуйте резервну копію назад в "Населені пункти"

## Приклад використання

Після очищення реєстру ваш основний код буде краще знаходити населені пункти, тому що:

**Було:**
- Текст: "Транспортні послуги по маршруту Малинівка-Степанки"
- Реєстр: ["с. Степанки", "Малинівка"] 
- Результат: Знайдено тільки "Малинівка"

**Стало:**
- Текст: "Транспортні послуги по маршруту Малинівка-Степанки"  
- Реєстр: ["Степанки", "Малинівка"]
- Результат: Знайдено "Малинівка" та "Степанки"
