// Код для очищення реєстру населених пунктів від префіксів
// Видаляє с., м., смт. та інші префікси з назв населених пунктів

// Константи
const SPREADSHEET_ID2 = '1nhLTKEsWa6AAT0xPHqOAc7TQFzkHtqcEu7w7KlAAogU';
const SETTLEMENTS_SHEET_NAME2 = 'Населені пункти';

// Функція для очищення назв населених пунктів від префіксів
function cleanSettlementsRegistry() {
  try {
    // Отримуємо аркуш з населеними пунктами
    const settlementsSheet = SpreadsheetApp.openById(SPREADSHEET_ID2).getSheetByName(SETTLEMENTS_SHEET_NAME2);
    
    if (!settlementsSheet) {
      Logger.log('Аркуш "Населені пункти" не знайдено');
      return;
    }
    
    const lastRow = settlementsSheet.getLastRow();
    if (lastRow < 1) {
      Logger.log('Аркуш "Населені пункти" порожній');
      return;
    }
    
    // Отримуємо всі дані зі стовпця A
    const settlementsData = settlementsSheet.getRange(1, 1, lastRow, 1).getValues();
    
    // Масив для очищених назв
    const cleanedSettlements = [];
    let changedCount = 0;
    
    Logger.log('=== Початок очищення реєстру населених пунктів ===');
    
    for (let i = 0; i < settlementsData.length; i++) {
      const originalName = settlementsData[i][0].toString().trim();
      
      if (originalName === '') {
        cleanedSettlements.push(['']);
        continue;
      }
      
      // Очищуємо назву від префіксів
      const cleanedName = cleanSettlementName(originalName);
      
      // Логуємо зміни
      if (originalName !== cleanedName) {
        Logger.log(`Рядок ${i + 1}: "${originalName}" → "${cleanedName}"`);
        changedCount++;
      }
      
      cleanedSettlements.push([cleanedName]);
    }
    
    // Записуємо очищені дані назад в аркуш
    if (cleanedSettlements.length > 0) {
      settlementsSheet.getRange(1, 1, cleanedSettlements.length, 1).setValues(cleanedSettlements);
    }
    
    Logger.log(`=== Очищення завершено ===`);
    Logger.log(`Всього записів: ${settlementsData.length}`);
    Logger.log(`Змінено записів: ${changedCount}`);
    Logger.log(`Незмінено записів: ${settlementsData.length - changedCount}`);
    
    // Показуємо повідомлення користувачу
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'Очищення завершено!', 
      `Оброблено ${settlementsData.length} записів.\nЗмінено ${changedCount} записів.\nДеталі в логах (Ctrl+Enter для перегляду).`, 
      ui.AlertType.INFO
    );
    
  } catch (error) {
    Logger.log('Помилка при очищенні реєстру: ' + error.toString());
    const ui = SpreadsheetApp.getUi();
    ui.alert('Помилка!', 'Сталася помилка при очищенні реєстру: ' + error.toString(), ui.AlertType.ERROR);
  }
}

// Функція для очищення окремої назви населеного пункту
function cleanSettlementName(name) {
  if (!name || typeof name !== 'string') {
    return '';
  }
  
  let cleaned = name.trim();
  
  // Видаляємо префікси на початку рядка
  // Регулярний вираз для пошуку префіксів: с., м., смт., сел., село, місто тощо
  cleaned = cleaned.replace(/^(с\.|м\.|смт\|смт\.|с-ще\|село\s+|місто\s+|селище\s+|селище\s+міського\s+типу\s+)\s*/i, '');
  
  // Видаляємо зайві пробіли
  cleaned = cleaned.replace(/\s+/g, ' ').trim();
  
  // Видаляємо розділові знаки в кінці
  cleaned = cleaned.replace(/[,.;]+$/, '');
  
  return cleaned;
}

// Функція для створення резервної копії перед очищенням
function createBackupBeforeClean() {
  try {
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);
    const settlementsSheet = spreadsheet.getSheetByName(SETTLEMENTS_SHEET_NAME);
    
    if (!settlementsSheet) {
      Logger.log('Аркуш "Населені пункти" не знайдено');
      return false;
    }
    
    // Створюємо копію аркуша
    const backupSheet = settlementsSheet.copyTo(spreadsheet);
    const timestamp = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), 'yyyy-MM-dd_HH-mm-ss');
    backupSheet.setName(`Населені пункти_backup_${timestamp}`);
    
    Logger.log(`Створено резервну копію: ${backupSheet.getName()}`);
    
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'Резервна копія створена!', 
      `Створено резервну копію аркуша: "${backupSheet.getName()}"`, 
      ui.AlertType.INFO
    );
    
    return true;
  } catch (error) {
    Logger.log('Помилка при створенні резервної копії: ' + error.toString());
    return false;
  }
}

// Функція для створення меню
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Очищення реєстру')
    .addItem('1. Створити резервну копію', 'createBackupBeforeClean')
    .addSeparator()
    .addItem('2. Тестувати очищення (без змін)', 'testCleanSettlements')
    .addSeparator()
    .addItem('3. Очистити реєстр населених пунктів', 'cleanSettlementsRegistry')
    .addToUi();
}
