// Simple test script to verify license plate extraction
// Copy and paste this into browser console to test

// Test the specific problematic case
const testText = "Перевезення вантажу за маршрутом Вільногірськ-Степанки, VOLVO AE6117OЕ,пр-п АЕ0717XF, ТТН 6322 від 27.11.2023";

console.log('Testing specific problematic case:');
console.log('Input:', testText);

// Test pattern matching
const upperText = testText.toUpperCase();
console.log('Uppercase text:', upperText);

// Test the flexible pattern
const flexiblePattern = /\b([AАBВEЕIІKКMМHНOОPРCСTТXХF]{2})\s*[-,.\s]*(\d{4})\s*[-,.\s]*([AАBВEЕIІKКMМHНOОPРCСTТXХF]{2})\b/g;
let match;
const foundPlates = [];

while ((match = flexiblePattern.exec(upperText)) !== null) {
  const plateCandidate = match[1] + match[2] + match[3];
  console.log('Found potential plate:', plateCandidate);
  console.log('Match details:', match);
  
  // Check context
  const beforeMatch = upperText.substring(Math.max(0, match.index - 15), match.index);
  console.log('Before match:', beforeMatch);
  
  // Skip if it looks like a document number
  if (!beforeMatch.match(/ТТН\s*№?\s*$/i) && 
      !beforeMatch.match(/№\s*$/i) &&
      !beforeMatch.match(/ВІД\s*$/i)) {
    foundPlates.push(plateCandidate);
    console.log('Added plate:', plateCandidate);
  } else {
    console.log('Skipped plate (document number):', plateCandidate);
  }
}

console.log('All found plates:', foundPlates);

// Expected results
console.log('Expected Vehicle: AE6117OЕ');
console.log('Expected Trailer: АЕ0717XF');
