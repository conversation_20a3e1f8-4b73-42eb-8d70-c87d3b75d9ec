# Ukrainian License Plate Extractor for Google Sheets

This Google Apps Script automatically extracts Ukrainian vehicle and trailer license plate numbers from text entries in Google Sheets.

## Features

- **Automatic Extraction**: Processes text in column A and extracts license plates
- **Vehicle & Trailer Separation**: Identifies vehicle plates (column B) and trailer plates (column C)
- **Format Handling**: Handles various text formats, spaces, mixed case, and Cyrillic/Latin character mixing
- **Ukrainian Standard**: Follows Ukrainian license plate format (2 letters + 4 digits + 2 letters)
- **Trailer Detection**: Automatically identifies trailer plates (those with "Х" prefix in suffix)
- **Multiple Processing Options**: Process entire sheet or selected range
- **Sample Data**: Includes test data for validation

## Ukrainian License Plate Format

Ukrainian license plates use the format: **XX0000XX** where:
- X = Valid Cyrillic letters: А, В, Е, І, К, М, Н, О, Р, С, Т, Х
- 0 = Digits (0-9)
- Trailer plates typically have "Х" as the first letter of the suffix

## Setup Instructions

### 1. Create a New Google Apps Script Project
1. Go to [script.google.com](https://script.google.com)
2. Click "New Project"
3. Replace the default code with the contents of `Code.js`
4. Save the project with a meaningful name (e.g., "License Plate Extractor")

### 2. Set Up Your Google Sheet
1. Create a new Google Sheet or open an existing one
2. Set up columns:
   - **Column A**: "Номенклатура товарів/послуг" (your text data)
   - **Column B**: "Vehicle License Plate" (will be populated automatically)
   - **Column C**: "Trailer License Plate" (will be populated automatically)

### 3. Link the Script to Your Sheet
1. In your Google Sheet, go to Extensions → Apps Script
2. Replace the default code with the contents of `Code.js`
3. Save the script
4. Refresh your Google Sheet

## Usage

### Menu Options
After setup, you'll see a "License Plate Extractor" menu in your Google Sheet with these options:

1. **Process All License Plates**: Processes all data in the sheet starting from row 2
2. **Process Selected Range**: Processes only the selected cells
3. **Test Extraction**: Runs test cases in the console (for debugging)
4. **Create Sample Data**: Creates sample data for testing
5. **Clear Results**: Clears extracted results from columns B and C

### Step-by-Step Usage
1. Enter your text data in column A (starting from row 2)
2. Go to "License Plate Extractor" menu
3. Click "Process All License Plates" or "Process Selected Range"
4. The script will automatically extract and populate:
   - Vehicle license plates in column B
   - Trailer license plates in column C

## Example Data

The script can handle various text formats:

```
Перевезення вантажу за маршрутом Вільногірськ – Степанки, а/м RENAULT AE5887КІ, н/пр. АЕ0234ХК, водій Нежинський М.
→ Vehicle: AE5887КІ, Trailer: АЕ0234ХК

Транспортна послуга у міжнародному сполученні Степанки (Україна) - Вільнюс (Литва) Строгий СВ6395ВХ СВ2061ХО
→ Vehicle: СВ6395ВХ, Trailer: СВ2061ХО

автомобіль DAF CA3927ІТ спеціалізований вантажний сідловий тягач -Е, Причіп: SCHWARZMULLER CА1451ХF
→ Vehicle: CA3927ІТ, Trailer: CА1451ХF
```

## Technical Details

### Text Normalization
- Converts text to uppercase
- Replaces Latin letters with Cyrillic equivalents (A→А, B→В, etc.)
- Handles spaces and various separators

### Pattern Recognition
- Primary pattern: Standard format with optional spaces
- Secondary pattern: Handles mixed separators (dashes, spaces)
- Tertiary pattern: Handles dots and other punctuation

### Plate Categorization
- **Vehicle plates**: First found plate or plates without "Х" prefix
- **Trailer plates**: Plates with "Х" as first letter of suffix, or second plate if multiple found

## Troubleshooting

### Common Issues
1. **No menu appears**: Refresh the sheet and wait a few seconds
2. **Script doesn't run**: Check that you've saved the script and have proper permissions
3. **Incorrect extraction**: Verify that the text contains valid Ukrainian license plates
4. **Permission errors**: Grant necessary permissions when prompted

### Testing
Use the "Test Extraction" menu option to run test cases and verify the script is working correctly.

## Support

For issues or improvements, check the script's test function which includes sample cases that demonstrate expected behavior.
